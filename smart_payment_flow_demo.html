<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Payment Flow Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-2xl font-bold mb-6 text-center">Smart Payment Flow Demo</h1>
        
        <!-- Scenario Selector -->
        <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">Test Scenario:</label>
            <select id="scenarioSelect" class="w-full p-3 border border-gray-300 rounded-lg">
                <option value="zero">₹0 Payment (No payment needed)</option>
                <option value="wallet-full">Wallet Covers Full Amount (₹100 needed, ₹150 wallet)</option>
                <option value="wallet-partial">Partial Wallet + Razorpay (₹100 needed, ₹30 wallet)</option>
                <option value="no-wallet">No Wallet Balance (₹100 needed, ₹0 wallet)</option>
                <option value="refund">Refund Scenario (₹50 refund to wallet)</option>
            </select>
        </div>

        <!-- Payment Breakdown Display -->
        <div id="paymentBreakdown" class="mb-6 p-4 bg-gray-50 rounded-lg">
            <h3 class="font-semibold mb-3">Payment Breakdown:</h3>
            <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                    <span>Amount:</span>
                    <span id="amount">₹100</span>
                </div>
                <div class="flex justify-between">
                    <span>Platform fee:</span>
                    <span id="platformFee">₹20</span>
                </div>
                <div class="flex justify-between" id="previousAmountRow">
                    <span>Previous amount:</span>
                    <span id="previousAmount">₹70</span>
                </div>
                <div class="flex justify-between">
                    <span>Wallet Balance:</span>
                    <span id="walletBalance">₹150</span>
                </div>
                <div class="border-t pt-2 flex justify-between font-semibold">
                    <span>Final Total:</span>
                    <span id="finalTotal" class="text-green-600">₹0</span>
                </div>
            </div>
        </div>

        <!-- Action Button -->
        <button id="bookPayButton" onclick="simulatePaymentFlow()" 
                class="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-bold py-3 px-6 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl disabled:opacity-50">
            <span id="buttonText">Book Now & Pay</span>
            <span id="buttonLoading" class="hidden">Processing...</span>
        </button>

        <!-- Result Display -->
        <div id="resultDisplay" class="mt-6 p-4 rounded-lg hidden">
            <h3 class="font-semibold mb-2">Payment Flow Result:</h3>
            <div id="resultContent" class="text-sm"></div>
        </div>

        <!-- Flow Explanation -->
        <div class="mt-6 p-4 bg-blue-50 rounded-lg">
            <h3 class="font-semibold mb-2 text-blue-800">How Smart Payment Flow Works:</h3>
            <ol class="text-sm text-blue-700 space-y-1">
                <li><strong>1. ₹0 Payment:</strong> Process immediately without payment gateway</li>
                <li><strong>2. Wallet Sufficient:</strong> Deduct from wallet automatically</li>
                <li><strong>3. Wallet Partial:</strong> Use wallet + Razorpay for remaining</li>
                <li><strong>4. No Wallet:</strong> Direct Razorpay payment</li>
                <li><strong>5. Refund:</strong> Credit amount to wallet automatically</li>
            </ol>
        </div>
    </div>

    <script>
        // Simulate different payment scenarios
        const scenarios = {
            zero: {
                amount: 50,
                platformFee: 20,
                previousAmount: 70,
                walletBalance: 100,
                finalTotal: 0,
                description: "No additional payment needed"
            },
            'wallet-full': {
                amount: 100,
                platformFee: 20,
                previousAmount: 0,
                walletBalance: 150,
                finalTotal: 120,
                description: "Wallet covers full amount"
            },
            'wallet-partial': {
                amount: 100,
                platformFee: 20,
                previousAmount: 0,
                walletBalance: 30,
                finalTotal: 120,
                description: "Partial wallet + Razorpay"
            },
            'no-wallet': {
                amount: 100,
                platformFee: 20,
                previousAmount: 0,
                walletBalance: 0,
                finalTotal: 120,
                description: "Full Razorpay payment"
            },
            refund: {
                amount: 50,
                platformFee: 20,
                previousAmount: 120,
                walletBalance: 50,
                finalTotal: -50,
                description: "Refund to wallet"
            }
        };

        // Update display based on selected scenario
        document.getElementById('scenarioSelect').addEventListener('change', function() {
            const scenario = scenarios[this.value];
            updateDisplay(scenario);
        });

        function updateDisplay(scenario) {
            document.getElementById('amount').textContent = `₹${scenario.amount}`;
            document.getElementById('platformFee').textContent = `₹${scenario.platformFee}`;
            document.getElementById('previousAmount').textContent = `₹${scenario.previousAmount}`;
            document.getElementById('walletBalance').textContent = `₹${scenario.walletBalance}`;
            
            const finalTotal = document.getElementById('finalTotal');
            finalTotal.textContent = `₹${Math.abs(scenario.finalTotal)}`;
            finalTotal.className = scenario.finalTotal <= 0 ? 'text-green-600' : 'text-red-600';
            
            // Show/hide previous amount row
            const previousAmountRow = document.getElementById('previousAmountRow');
            if (scenario.previousAmount > 0) {
                previousAmountRow.classList.remove('hidden');
            } else {
                previousAmountRow.classList.add('hidden');
            }
        }

        function simulatePaymentFlow() {
            const scenario = scenarios[document.getElementById('scenarioSelect').value];
            const button = document.getElementById('bookPayButton');
            const buttonText = document.getElementById('buttonText');
            const buttonLoading = document.getElementById('buttonLoading');
            const resultDisplay = document.getElementById('resultDisplay');
            const resultContent = document.getElementById('resultContent');

            // Show loading state
            button.disabled = true;
            buttonText.classList.add('hidden');
            buttonLoading.classList.remove('hidden');

            // Simulate payment processing
            setTimeout(() => {
                let result = '';
                
                if (scenario.finalTotal === 0) {
                    result = `
                        <div class="text-green-600">
                            <strong>✅ Zero Payment Flow</strong><br>
                            Booking processed immediately without payment gateway.<br>
                            Message: "Booking updated successfully! No additional payment required."
                        </div>
                    `;
                } else if (scenario.finalTotal > 0) {
                    if (scenario.walletBalance >= scenario.finalTotal) {
                        result = `
                            <div class="text-blue-600">
                                <strong>💰 Wallet-Only Payment</strong><br>
                                ₹${scenario.finalTotal} deducted from wallet automatically.<br>
                                New wallet balance: ₹${scenario.walletBalance - scenario.finalTotal}<br>
                                Message: "Booking confirmed! ₹${scenario.finalTotal} deducted from your wallet."
                            </div>
                        `;
                    } else if (scenario.walletBalance > 0) {
                        const razorpayAmount = scenario.finalTotal - scenario.walletBalance;
                        result = `
                            <div class="text-purple-600">
                                <strong>🔄 Hybrid Payment</strong><br>
                                Wallet usage: ₹${scenario.walletBalance}<br>
                                Razorpay payment: ₹${razorpayAmount}<br>
                                Total paid: ₹${scenario.finalTotal}<br>
                                Message: "Booking confirmed! ₹${scenario.finalTotal} paid (₹${razorpayAmount} online + ₹${scenario.walletBalance} from wallet)"
                            </div>
                        `;
                    } else {
                        result = `
                            <div class="text-orange-600">
                                <strong>💳 Razorpay Payment</strong><br>
                                Full amount via Razorpay: ₹${scenario.finalTotal}<br>
                                Message: "Booking confirmed! ₹${scenario.finalTotal} paid online."
                            </div>
                        `;
                    }
                } else {
                    const refundAmount = Math.abs(scenario.finalTotal);
                    result = `
                        <div class="text-green-600">
                            <strong>💸 Refund Scenario</strong><br>
                            ₹${refundAmount} credited to wallet automatically.<br>
                            New wallet balance: ₹${scenario.walletBalance + refundAmount}<br>
                            Message: "Booking updated successfully! ₹${refundAmount} has been credited to your wallet."
                        </div>
                    `;
                }

                // Show result
                resultContent.innerHTML = result;
                resultDisplay.classList.remove('hidden');

                // Reset button
                button.disabled = false;
                buttonText.classList.remove('hidden');
                buttonLoading.classList.add('hidden');
            }, 2000);
        }

        // Initialize with first scenario
        updateDisplay(scenarios.zero);
    </script>
</body>
</html>
