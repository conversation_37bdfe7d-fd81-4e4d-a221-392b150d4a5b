<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Breakdown Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
        <h2 class="text-xl font-bold mb-4">Booking Update Payment Breakdown Test</h2>
        
        <!-- Simulate the payment breakdown display -->
        <div id="paymentBreakdownDisplay" class="mt-2 pt-2 border-t border-purple-200">
            <div class="bg-gray-50 rounded-lg p-3">
                <div class="text-base font-semibold text-gray-800 mb-2">Payment Breakdown:</div>
                <table class="w-full text-sm">
                    <tr class="border-b border-gray-200">
                        <td class="py-2 text-gray-600">Amount</td>
                        <td id="breakdownAmount" class="py-2 text-right font-semibold">₹0</td>
                    </tr>
                    <tr id="platformFeeRow" class="border-b border-gray-200">
                        <td class="py-2 text-gray-600">Platform fee</td>
                        <td id="breakdownPlatformFee" class="py-2 text-right text-gray-600">₹0</td>
                    </tr>
                    <tr id="walletBalanceRow" class="border-b border-gray-200">
                        <td class="py-2 text-gray-600">Previous amount:</td>
                        <td id="breakdownWallet" class="py-2 text-right text-gray-600">₹0</td>
                    </tr>
                    <tr id="needToPayOnlineRow" class="border-b border-gray-200 hidden">
                        <td class="py-2 text-gray-600">Need to pay online</td>
                        <td id="breakdownOnline" class="py-2 text-right text-red-600">₹0</td>
                    </tr>
                    <tr id="creditToWalletRow" class="border-b border-gray-200 hidden">
                        <td class="py-2 text-gray-600">Credit to wallet</td>
                        <td id="breakdownCredit" class="py-2 text-right text-gray-600">₹0</td>
                    </tr>
                    <tr class="border-t-2 border-gray-800 font-semibold">
                        <td class="py-2 text-gray-800">Final Total</td>
                        <td id="breakdownFinal" class="py-2 text-right text-lg text-green-600">₹0</td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="mt-6 space-y-4">
            <button onclick="testNewBooking()" class="w-full bg-blue-500 text-white py-2 px-4 rounded">
                Test New Booking (15 min → 30 min billing)
            </button>
            <button onclick="testBookingUpdate()" class="w-full bg-green-500 text-white py-2 px-4 rounded">
                Test Booking Update (30 min → 60 min)
            </button>
        </div>

        <div id="result" class="mt-4 p-3 bg-blue-50 rounded hidden">
            <h3 class="font-semibold">Expected vs Actual:</h3>
            <div id="resultContent"></div>
        </div>
    </div>

    <script>
        // Simulate the original booking data
        window.originalBooking = {
            total_amount: 70 // ₹50 base + ₹20 platform fee
        };

        // Mock user data
        const hourlyRate = 100;
        const platformFeeAmount = 20;
        const walletBalance = 0;

        function updatePaymentBreakdown(totalAmount, billingDuration, actualDuration) {
            const baseAmount = hourlyRate * billingDuration;
            const isUpdate = window.updateBookingId && window.originalBooking;
            
            // Show base amount in Amount row
            const breakdownText = `₹${hourlyRate}/h × ${billingDuration} = ₹${Math.round(baseAmount)}`;
            document.getElementById('breakdownAmount').textContent = breakdownText;

            // Handle platform fee display
            const platformFeeRow = document.getElementById('platformFeeRow');
            const platformFeeElement = document.getElementById('breakdownPlatformFee');
            if (platformFeeAmount > 0) {
                platformFeeRow.classList.remove('hidden');
                platformFeeElement.textContent = '₹' + platformFeeAmount;
            } else {
                platformFeeRow.classList.add('hidden');
            }

            if (isUpdate) {
                // For booking updates, show previous amount and calculate difference
                const originalAmount = parseFloat(window.originalBooking.total_amount);
                const difference = totalAmount - originalAmount;
                
                // Show previous amount in wallet balance row
                const walletBalanceRow = document.getElementById('walletBalanceRow');
                const walletElement = document.getElementById('breakdownWallet');
                const walletLabel = walletBalanceRow.querySelector('td:first-child');
                walletLabel.textContent = 'Previous amount:';
                walletElement.textContent = '₹' + Math.round(originalAmount);
                walletElement.className = 'py-2 text-right text-gray-600';
                walletBalanceRow.classList.remove('hidden');

                // Hide other rows for updates
                document.getElementById('needToPayOnlineRow').classList.add('hidden');
                document.getElementById('creditToWalletRow').classList.add('hidden');

                // Final total shows difference (0 if no difference or negative)
                const finalTotal = Math.max(0, difference);
                document.getElementById('breakdownFinal').textContent = '₹' + Math.round(finalTotal);

                // Update colors for final total
                const finalEl = document.getElementById('breakdownFinal');
                finalEl.className = 'py-2 text-right text-lg ' + (finalTotal > 0 ? 'text-red-600' : 'text-green-600');
            } else {
                // For new bookings, show normal breakdown
                const walletUsage = Math.min(walletBalance, totalAmount);
                const finalNeedToPayOnline = Math.max(0, (baseAmount + platformFeeAmount) - walletUsage);

                // Handle wallet balance display
                const walletBalanceRow = document.getElementById('walletBalanceRow');
                const walletElement = document.getElementById('breakdownWallet');
                const walletLabel = walletBalanceRow.querySelector('td:first-child');
                walletLabel.textContent = 'Wallet Balance';
                if (walletUsage > 0) {
                    walletBalanceRow.classList.remove('hidden');
                    walletElement.textContent = '-₹' + Math.round(walletUsage);
                    walletElement.className = 'py-2 text-right text-green-600';
                } else {
                    walletBalanceRow.classList.add('hidden');
                }

                // Handle need to pay online display
                const needToPayOnlineRow = document.getElementById('needToPayOnlineRow');
                const onlineElement = document.getElementById('breakdownOnline');
                if (finalNeedToPayOnline > 0) {
                    needToPayOnlineRow.classList.remove('hidden');
                    onlineElement.textContent = '₹' + Math.round(finalNeedToPayOnline);
                    onlineElement.className = 'py-2 text-right text-red-600';
                } else {
                    needToPayOnlineRow.classList.add('hidden');
                }

                // Final total
                document.getElementById('breakdownFinal').textContent = '₹' + Math.round(finalNeedToPayOnline);

                // Update colors for final total
                const finalEl = document.getElementById('breakdownFinal');
                finalEl.className = 'py-2 text-right text-lg ' + (finalNeedToPayOnline > 0 ? 'text-red-600' : 'text-green-600');
            }
        }

        function testNewBooking() {
            // Reset for new booking
            window.updateBookingId = null;
            window.originalBooking = null;
            
            // Test: 15 minutes actual, 30 minutes billing
            const totalAmount = 70; // ₹50 base + ₹20 platform fee
            updatePaymentBreakdown(totalAmount, 0.5, 0.25);
            
            showResult("New Booking Test", "Amount: ₹100/h × 0.5 = ₹50<br>Platform fee: ₹20<br>Final Total: ₹70");
        }

        function testBookingUpdate() {
            // Set up for booking update
            window.updateBookingId = 123;
            window.originalBooking = {
                total_amount: 70 // Previous: ₹50 base + ₹20 platform fee
            };
            
            // Test: Update to 1 hour
            const totalAmount = 120; // ₹100 base + ₹20 platform fee
            updatePaymentBreakdown(totalAmount, 1.0, 1.0);
            
            showResult("Booking Update Test", "Amount: ₹100/h × 1.0 = ₹100<br>Platform fee: ₹20<br>Previous amount: ₹70<br>Final Total: ₹0 (difference already calculated)");
        }

        function showResult(title, expected) {
            const resultDiv = document.getElementById('result');
            const contentDiv = document.getElementById('resultContent');
            contentDiv.innerHTML = `<strong>${title}</strong><br><br><strong>Expected:</strong><br>${expected}`;
            resultDiv.classList.remove('hidden');
        }

        // Initialize with new booking test
        testNewBooking();
    </script>
</body>
</html>
